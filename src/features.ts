interface FeaturesConfig {
  routerTransform: Record<string, boolean>;
  regulations: string[];
  supportSourceTransformV1: true;
  supportTransformerProxyV1: true;
  upgradedToSourceTransformV2: true;
  supportDestTransformCompactedPayloadV1: true;
}

const defaultFeaturesConfig: FeaturesConfig = {
  routerTransform: {
    ACTIVE_CAMPAIGN: true,
    ALGOLIA: true,
    CANDU: true,
    DELIGHTED: true,
    DRIP: true,
    FB_CUSTOM_AUDIENCE: true,
    GA: true,
    GAINSIGHT: true,
    GAINSIGHT_PX: true,
    GOOGLESHEETS: true,
    GOOGLE_ADWORDS_ENHANCED_CONVERSIONS: true,
    GOOGLE_ADWORDS_REMARKETING_LISTS: true,
    GOOGLE_ADWORDS_OFFLINE_CONVERSIONS: true,
    HS: true,
    ITERABLE: true,
    KLAVIYO: true,
    KUSTOMER: true,
    MAILCHIMP: true,
    MAILMODO: true,
    MARKETO: true,
    OMETRIA: true,
    PARDOT: true,
    PINTEREST_TAG: true,
    PROFITWELL: true,
    SALESFORCE: true,
    SALESFORCE_OAUTH: true,
    SALESFORCE_OAUTH_SANDBOX: true,
    SFMC: true,
    SNAPCHAT_CONVERSION: true,
    TIKTOK_ADS: true,
    TRENGO: true,
    YAHOO_DSP: true,
    CANNY: true,
    LAMBDA: true,
    WOOTRIC: true,
    GOOGLE_CLOUD_FUNCTION: true,
    BQSTREAM: true,
    CLICKUP: true,
    FRESHMARKETER: true,
    FRESHSALES: true,
    MONDAY: true,
    CUSTIFY: true,
    USER: true,
    REFINER: true,
    FACEBOOK_OFFLINE_CONVERSIONS: true,
    MAILJET: true,
    SNAPCHAT_CUSTOM_AUDIENCE: true,
    MARKETO_STATIC_LIST: true,
    CAMPAIGN_MANAGER: true,
    SENDGRID: true,
    SENDINBLUE: true,
    ZENDESK: true,
    MP: true,
    TIKTOK_ADS_OFFLINE_EVENTS: true,
    CRITEO_AUDIENCE: true,
    CUSTOMERIO: true,
    BRAZE: true,
    OPTIMIZELY_FULLSTACK: true,
    TWITTER_ADS: true,
    CLEVERTAP: true,
    ORTTO: true,
    GLADLY: true,
    ONE_SIGNAL: true,
    TIKTOK_AUDIENCE: true,
    REDDIT: true,
    THE_TRADE_DESK: true,
    INTERCOM: true,
    NINETAILED: true,
    KOALA: true,
    LINKEDIN_ADS: true,
    BLOOMREACH: true,
    MOVABLE_INK: true,
    EMARSYS: true,
    KODDI: true,
    WUNDERKIND: true,
    CLICKSEND: true,
    ZOHO: true,
    ZOHO_DEV: true,
    CORDIAL: true,
    X_AUDIENCE: true,
    BLOOMREACH_CATALOG: true,
    SMARTLY: true,
    HTTP: true,
    AMAZON_AUDIENCE: true,
    INTERCOM_V2: true,
    LINKEDIN_AUDIENCE: true,
    TOPSORT: true,
    CUSTOMERIO_AUDIENCE: true,
    ACCOIL_ANALYTICS: true,
  },
  regulations: [
    'BRAZE',
    'AM',
    'INTERCOM',
    'CLEVERTAP',
    'AF',
    'MP',
    'GA',
    'ITERABLE',
    'ENGAGE',
    'CUSTIFY',
    'SENDGRID',
    'SPRIG',
    'EMARSYS',
  ],
  supportSourceTransformV1: true,
  supportTransformerProxyV1: true,
  upgradedToSourceTransformV2: true,
  supportDestTransformCompactedPayloadV1: true,
};

export default defaultFeaturesConfig;
