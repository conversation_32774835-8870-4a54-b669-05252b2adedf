import { authHeader1, secret1, secret2 } from '../maskedSecrets';
const mockFns = (_) => {
  jest.spyOn(Date, 'now').mockReturnValue(new Date('2023-10-14T00:00:00.000Z').valueOf());
};

export const data = [
  {
    name: 'moengage',
    description: 'Test 0: Track event with nested arrays and product properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                timezone: 'Asia/Tokyo',
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              event: 'Order Completed',
              integrations: { All: true },
              messageId: 'a0adfab9-baf7-4e09-a2ce-bbe2844c324a',
              originalTimestamp: '2020-10-16T08:10:12.782Z',
              properties: {
                checkout_id: 'what is checkout id here??',
                coupon: 'APPARELSALE',
                currency: 'GBP',
                order_id: 'transactionId',
                category: 'some category',
                originalArray: [
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                ],
                products: [
                  {
                    brand: '',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/bacon-jam.jpg',
                    name: 'Food/Drink',
                    position: 1,
                    price: 3,
                    product_id: 'product-bacon-jam',
                    quantity: 2,
                    sku: 'sku-1',
                    typeOfProduct: 'Food',
                    url: 'https://www.example.com/product/bacon-jam',
                    value: 6,
                    variant: 'Extra topped',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/t-shirt.jpg',
                    name: 'T-Shirt',
                    position: 2,
                    price: 12.99,
                    product_id: 'product-t-shirt',
                    quantity: 1,
                    sku: 'sku-2',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/t-shirt',
                    value: 12.99,
                    variant: 'White',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    coupon: 'APPARELSALE',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/offer-t-shirt.jpg',
                    name: 'T-Shirt-on-offer',
                    position: 1,
                    price: 12.99,
                    product_id: 'offer-t-shirt',
                    quantity: 1,
                    sku: 'sku-3',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/offer-t-shirt',
                    value: 12.99,
                    variant: 'Black',
                  },
                ],
                revenue: 31.98,
                shipping: 4,
                value: 31.98,
              },
              receivedAt: '2020-10-16T13:40:12.792+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:10:12.783Z',
              timestamp: '2020-10-16T13:40:12.791+05:30',
              type: 'track',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: `https://api-01.moengage.com/v1/event/${secret1}`,
              headers: {
                'Content-Type': 'application/json',
                'MOE-APPKEY': secret1,
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  customer_id: 'rudder123',
                  type: 'event',
                  actions: [
                    {
                      action: 'Order Completed',
                      attributes: {
                        category: 'some category',
                        'originalArray[0].nested_field': 'nested value',
                        'originalArray[0].tags[0]': 'tag_1',
                        'originalArray[0].tags[1]': 'tag_2',
                        'originalArray[0].tags[2]': 'tag_3',
                        'originalArray[1].nested_field': 'nested value',
                        'originalArray[1].tags[0]': 'tag_1',
                        'originalArray[1].tags[1]': 'tag_2',
                        'originalArray[1].tags[2]': 'tag_3',
                        'originalArray[2].nested_field': 'nested value',
                        'originalArray[2].tags[0]': 'tag_1',
                        'originalArray[2].tags[1]': 'tag_2',
                        'originalArray[2].tags[2]': 'tag_3',
                        'originalArray[3].nested_field': 'nested value',
                        'originalArray[3].tags[0]': 'tag_1',
                        'originalArray[3].tags[1]': 'tag_2',
                        'originalArray[3].tags[2]': 'tag_3',
                        'originalArray[4].nested_field': 'nested value',
                        'originalArray[4].tags[0]': 'tag_1',
                        'originalArray[4].tags[1]': 'tag_2',
                        'originalArray[4].tags[2]': 'tag_3',
                        'originalArray[5].nested_field': 'nested value',
                        'originalArray[5].tags[0]': 'tag_1',
                        'originalArray[5].tags[1]': 'tag_2',
                        'originalArray[5].tags[2]': 'tag_3',
                        'originalArray[6].nested_field': 'nested value',
                        'originalArray[6].tags[0]': 'tag_1',
                        'originalArray[6].tags[1]': 'tag_2',
                        'originalArray[6].tags[2]': 'tag_3',
                        'originalArray[7].nested_field': 'nested value',
                        'originalArray[7].tags[0]': 'tag_1',
                        'originalArray[7].tags[1]': 'tag_2',
                        'originalArray[7].tags[2]': 'tag_3',
                        'originalArray[8].nested_field': 'nested value',
                        'originalArray[8].tags[0]': 'tag_1',
                        'originalArray[8].tags[1]': 'tag_2',
                        'originalArray[8].tags[2]': 'tag_3',
                        checkout_id: 'what is checkout id here??',
                        coupon: 'APPARELSALE',
                        currency: 'GBP',
                        order_id: 'transactionId',
                        'products[0].brand': '',
                        'products[0].category': 'Merch',
                        'products[0].currency': 'GBP',
                        'products[0].image_url': 'https://www.example.com/product/bacon-jam.jpg',
                        'products[0].name': 'Food/Drink',
                        'products[0].position': 1,
                        'products[0].price': 3,
                        'products[0].product_id': 'product-bacon-jam',
                        'products[0].quantity': 2,
                        'products[0].sku': 'sku-1',
                        'products[0].typeOfProduct': 'Food',
                        'products[0].url': 'https://www.example.com/product/bacon-jam',
                        'products[0].value': 6,
                        'products[0].variant': 'Extra topped',
                        'products[1].brand': 'Levis',
                        'products[1].category': 'Merch',
                        'products[1].currency': 'GBP',
                        'products[1].image_url': 'https://www.example.com/product/t-shirt.jpg',
                        'products[1].name': 'T-Shirt',
                        'products[1].position': 2,
                        'products[1].price': 12.99,
                        'products[1].product_id': 'product-t-shirt',
                        'products[1].quantity': 1,
                        'products[1].sku': 'sku-2',
                        'products[1].typeOfProduct': 'Shirt',
                        'products[1].url': 'https://www.example.com/product/t-shirt',
                        'products[1].value': 12.99,
                        'products[1].variant': 'White',
                        'products[2].brand': 'Levis',
                        'products[2].category': 'Merch',
                        'products[2].coupon': 'APPARELSALE',
                        'products[2].currency': 'GBP',
                        'products[2].image_url':
                          'https://www.example.com/product/offer-t-shirt.jpg',
                        'products[2].name': 'T-Shirt-on-offer',
                        'products[2].position': 1,
                        'products[2].price': 12.99,
                        'products[2].product_id': 'offer-t-shirt',
                        'products[2].quantity': 1,
                        'products[2].sku': 'sku-3',
                        'products[2].typeOfProduct': 'Shirt',
                        'products[2].url': 'https://www.example.com/product/offer-t-shirt',
                        'products[2].value': 12.99,
                        'products[2].variant': 'Black',
                        revenue: 31.98,
                        shipping: 4,
                        value: 31.98,
                      },
                      app_version: '1.1.6',
                      current_time: '2020-10-16T13:40:12.791+05:30',
                      user_timezone_offset: 32400,
                      platform: 'web',
                    },
                  ],
                },
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
              },
              files: {},
              userId: 'rudder123',
            },
            statusCode: 200,
          },
        ],
      },
    },
    mockFns,
  },
  {
    name: 'moengage',
    description: 'Test 1: Identify call with user traits',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: 'e108eb05-f6cd-4624-ba8c-568f2e2b3f92',
              originalTimestamp: '2020-10-16T08:26:14.938Z',
              receivedAt: '2020-10-16T13:56:14.945+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:26:14.939Z',
              timestamp: '2020-10-16T13:56:14.944+05:30',
              type: 'identify',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'customer',
                  attributes: {
                    name: 'Rudder Test',
                    plan: 'Enterprise',
                    email: '<EMAIL>',
                    createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                    'company.id': 'abc123',
                    created_time: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/customer/${secret1}`,
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 2: Identify call with custom traits',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              traits: {
                CID: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
                RC_DATE: '2021-03-22T12:36:34Z',
                RC_NO_OF_SKUS: 1,
                RC_PN_SKU_LIST: '9317',
              },
              messageId: 'e108eb05-f6cd-4624-ba8c-568f2e2b3f92',
              originalTimestamp: '2020-10-16T08:26:14.938Z',
              receivedAt: '2020-10-16T13:56:14.945+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:26:14.939Z',
              timestamp: '2020-10-16T13:56:14.944+05:30',
              type: 'identify',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'customer',
                  attributes: {
                    CID: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
                    RC_DATE: '2021-03-22T12:36:34Z',
                    RC_NO_OF_SKUS: 1,
                    RC_PN_SKU_LIST: '9317',
                  },
                  customer_id: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/customer/${secret1}`,
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 3: Identify call with device information for Android',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                device: {
                  id: '7e32188a4dab669f',
                  manufacturer: 'Google',
                  model: 'Android SDK built for x86',
                  name: 'generic_x86',
                  token: 'desuhere',
                  type: 'android',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: '531e3507-1ef5-4a06-b83c-cb521ff34f0c',
              originalTimestamp: '2020-10-16T08:53:29.386Z',
              receivedAt: '2020-10-16T14:23:29.402+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:53:29.387Z',
              timestamp: '2020-10-16T14:23:29.401+05:30',
              type: 'identify',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'customer',
                  attributes: {
                    name: 'Rudder Test',
                    plan: 'Enterprise',
                    email: '<EMAIL>',
                    createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                    'company.id': 'abc123',
                    created_time: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/customer/${secret1}`,
            },
            statusCode: 200,
          },
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'device',
                  device_id: '7e32188a4dab669f',
                  attributes: {
                    model: 'Android SDK built for x86',
                    push_id: 'desuhere',
                    platform: 'android',
                    app_version: '1.1.6',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/device/${secret1}`,
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 4: Identify call with EU region configuration',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: 'a61357dd-e29e-4033-b1af-029625947fec',
              originalTimestamp: '2020-10-16T09:05:11.001Z',
              receivedAt: '2020-10-16T14:35:11.014+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T09:05:11.002Z',
              timestamp: '2020-10-16T14:35:11.013+05:30',
              type: 'identify',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'EU',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'customer',
                  attributes: {
                    name: 'Rudder Test',
                    plan: 'Enterprise',
                    email: '<EMAIL>',
                    createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                    'company.id': 'abc123',
                    created_time: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-02.moengage.com/v1/customer/${secret1}`,
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 5: Pass traits as object with nested properties',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              properties: {
                key1: {
                  key1_1: 'value1',
                  key1_2: 'value2',
                  data: 'hello',
                },
                data: {
                  key2: 'value2',
                },
                key2: 'value2',
              },
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                device: {
                  id: '7e32188a4dab669f',
                  manufacturer: 'Google',
                  model: 'Android SDK built for x86',
                  name: 'generic_x86',
                  token: 'desuhere',
                  type: 'android',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                  address: {
                    city: 'San Francisco',
                    country: 'USA',
                  },
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: '531e3507-1ef5-4a06-b83c-cb521ff34f0c',
              originalTimestamp: '2020-10-16T08:53:29.386Z',
              receivedAt: '2020-10-16T14:23:29.402+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:53:29.387Z',
              timestamp: '2020-10-16T14:23:29.401+05:30',
              type: 'identify',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
                useObjectData: true,
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'customer',
                  attributes: {
                    name: 'Rudder Test',
                    plan: 'Enterprise',
                    email: '<EMAIL>',
                    createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                    company: { id: 'abc123' },
                    address: {
                      city: 'San Francisco',
                      country: 'USA',
                    },
                    created_time: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/customer/${secret1}`,
            },
            statusCode: 200,
          },
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'device',
                  device_id: '7e32188a4dab669f',
                  attributes: {
                    model: 'Android SDK built for x86',
                    push_id: 'desuhere',
                    platform: 'android',
                    app_version: '1.1.6',
                    key1: {
                      key1_1: 'value1',
                      key1_2: 'value2',
                      data: 'hello',
                    },
                    data: {
                      key2: 'value2',
                    },
                    key2: 'value2',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/device/${secret1}`,
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 6: Identify call with IND region configuration',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: '9eb2f7c0-d896-494e-b105-60f604ce2906',
              originalTimestamp: '2020-10-16T09:09:31.465Z',
              receivedAt: '2020-10-16T14:39:31.468+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T09:09:31.466Z',
              timestamp: '2020-10-16T14:39:31.467+05:30',
              type: 'identify',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'IND',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'customer',
                  attributes: {
                    name: 'Rudder Test',
                    plan: 'Enterprise',
                    email: '<EMAIL>',
                    createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                    'company.id': 'abc123',
                    created_time: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-03.moengage.com/v1/customer/${secret1}`,
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 7: Invalid region configuration',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: '9eb2f7c0-d896-494e-b105-60f604ce2906',
              originalTimestamp: '2020-10-16T09:09:31.465Z',
              receivedAt: '2020-10-16T14:39:31.468+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T09:09:31.466Z',
              timestamp: '2020-10-16T14:39:31.467+05:30',
              type: 'identify',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'AMA',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error: 'The region is not valid',
            statTags: {
              destType: 'MOENGAGE',
              errorCategory: 'dataValidation',
              errorType: 'configuration',
              feature: 'processor',
              implementation: 'native',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 8: Missing event type validation',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: '9eb2f7c0-d896-494e-b105-60f604ce2906',
              originalTimestamp: '2020-10-16T09:09:31.465Z',
              receivedAt: '2020-10-16T14:39:31.468+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T09:09:31.466Z',
              timestamp: '2020-10-16T14:39:31.467+05:30',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'IND',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error: 'Event type is required',
            statTags: {
              destType: 'MOENGAGE',
              errorCategory: 'dataValidation',
              errorType: 'instrumentation',
              feature: 'processor',
              implementation: 'native',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 9: Unsupported event type validation',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: '9eb2f7c0-d896-494e-b105-60f604ce2906',
              originalTimestamp: '2020-10-16T09:09:31.465Z',
              receivedAt: '2020-10-16T14:39:31.468+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T09:09:31.466Z',
              timestamp: '2020-10-16T14:39:31.467+05:30',
              type: 'gone',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'IND',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error: 'Event type gone is not supported',
            statTags: {
              destType: 'MOENGAGE',
              errorCategory: 'dataValidation',
              errorType: 'instrumentation',
              feature: 'processor',
              implementation: 'native',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 10: Track event with timezone offset',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              channel: 'web',
              context: {
                timezone: 'Asia/Kolkata',
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              event: 'Order Completed',
              integrations: { All: true },
              messageId: 'a0adfab9-baf7-4e09-a2ce-bbe2844c324a',
              originalTimestamp: '2020-10-16T08:10:12.782Z',
              properties: {
                category: 'some category',
                originalArray: [
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  {
                    nested_field: 'nested value',
                    tags: ['tag_1', 'tag_2', 'tag_3', 'tag_1', 'tag_2', 'tag_3'],
                  },
                  {
                    nested_field: 'nested value',
                    tags: ['tag_1', 'tag_2', 'tag_3', 'tag_1', 'tag_2', 'tag_3'],
                  },
                ],
                checkout_id: 'what is checkout id here??',
                coupon: 'APPARELSALE',
                currency: 'GBP',
                order_id: 'transactionId',
                products: [
                  {
                    brand: '',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/bacon-jam.jpg',
                    name: 'Food/Drink',
                    position: 1,
                    price: 3,
                    product_id: 'product-bacon-jam',
                    quantity: 2,
                    sku: 'sku-1',
                    typeOfProduct: 'Food',
                    url: 'https://www.example.com/product/bacon-jam',
                    value: 6,
                    variant: 'Extra topped',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/t-shirt.jpg',
                    name: 'T-Shirt',
                    position: 2,
                    price: 12.99,
                    product_id: 'product-t-shirt',
                    quantity: 1,
                    sku: 'sku-2',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/t-shirt',
                    value: 12.99,
                    variant: 'White',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    coupon: 'APPARELSALE',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/offer-t-shirt.jpg',
                    name: 'T-Shirt-on-offer',
                    position: 1,
                    price: 12.99,
                    product_id: 'offer-t-shirt',
                    quantity: 1,
                    sku: 'sku-3',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/offer-t-shirt',
                    value: 12.99,
                    variant: 'Black',
                  },
                ],
                revenue: 31.98,
                shipping: 4,
                value: 31.98,
              },
              receivedAt: '2020-10-16T13:40:12.792+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:10:12.783Z',
              timestamp: '2020-10-16T13:40:12.791+05:30',
              type: 'track',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: `https://api-01.moengage.com/v1/event/${secret1}`,
              headers: {
                'Content-Type': 'application/json',
                'MOE-APPKEY': secret1,
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  customer_id: 'rudder123',
                  type: 'event',
                  actions: [
                    {
                      action: 'Order Completed',
                      attributes: {
                        checkout_id: 'what is checkout id here??',
                        coupon: 'APPARELSALE',
                        currency: 'GBP',
                        order_id: 'transactionId',
                        category: 'some category',
                        'originalArray[0].nested_field': 'nested value',
                        'originalArray[0].tags[0]': 'tag_1',
                        'originalArray[0].tags[1]': 'tag_2',
                        'originalArray[0].tags[2]': 'tag_3',
                        'originalArray[1].nested_field': 'nested value',
                        'originalArray[1].tags[0]': 'tag_1',
                        'originalArray[1].tags[1]': 'tag_2',
                        'originalArray[1].tags[2]': 'tag_3',
                        'originalArray[2].nested_field': 'nested value',
                        'originalArray[2].tags[0]': 'tag_1',
                        'originalArray[2].tags[1]': 'tag_2',
                        'originalArray[2].tags[2]': 'tag_3',
                        'originalArray[3].nested_field': 'nested value',
                        'originalArray[3].tags[0]': 'tag_1',
                        'originalArray[3].tags[1]': 'tag_2',
                        'originalArray[3].tags[2]': 'tag_3',
                        'originalArray[4].nested_field': 'nested value',
                        'originalArray[4].tags[0]': 'tag_1',
                        'originalArray[4].tags[1]': 'tag_2',
                        'originalArray[4].tags[2]': 'tag_3',
                        'originalArray[5].nested_field': 'nested value',
                        'originalArray[5].tags[0]': 'tag_1',
                        'originalArray[5].tags[1]': 'tag_2',
                        'originalArray[5].tags[2]': 'tag_3',
                        'originalArray[6].nested_field': 'nested value',
                        'originalArray[6].tags[0]': 'tag_1',
                        'originalArray[6].tags[1]': 'tag_2',
                        'originalArray[6].tags[2]': 'tag_3',
                        'originalArray[7].nested_field': 'nested value',
                        'originalArray[7].tags[0]': 'tag_1',
                        'originalArray[7].tags[1]': 'tag_2',
                        'originalArray[7].tags[2]': 'tag_3',
                        'originalArray[7].tags[3]': 'tag_1',
                        'originalArray[7].tags[4]': 'tag_2',
                        'originalArray[7].tags[5]': 'tag_3',
                        'originalArray[8].nested_field': 'nested value',
                        'originalArray[8].tags[0]': 'tag_1',
                        'originalArray[8].tags[1]': 'tag_2',
                        'originalArray[8].tags[2]': 'tag_3',
                        'originalArray[8].tags[3]': 'tag_1',
                        'originalArray[8].tags[4]': 'tag_2',
                        'originalArray[8].tags[5]': 'tag_3',
                        'products[0].brand': '',
                        'products[0].category': 'Merch',
                        'products[0].currency': 'GBP',
                        'products[0].image_url': 'https://www.example.com/product/bacon-jam.jpg',
                        'products[0].name': 'Food/Drink',
                        'products[0].position': 1,
                        'products[0].price': 3,
                        'products[0].product_id': 'product-bacon-jam',
                        'products[0].quantity': 2,
                        'products[0].sku': 'sku-1',
                        'products[0].typeOfProduct': 'Food',
                        'products[0].url': 'https://www.example.com/product/bacon-jam',
                        'products[0].value': 6,
                        'products[0].variant': 'Extra topped',
                        'products[1].brand': 'Levis',
                        'products[1].category': 'Merch',
                        'products[1].currency': 'GBP',
                        'products[1].image_url': 'https://www.example.com/product/t-shirt.jpg',
                        'products[1].name': 'T-Shirt',
                        'products[1].position': 2,
                        'products[1].price': 12.99,
                        'products[1].product_id': 'product-t-shirt',
                        'products[1].quantity': 1,
                        'products[1].sku': 'sku-2',
                        'products[1].typeOfProduct': 'Shirt',
                        'products[1].url': 'https://www.example.com/product/t-shirt',
                        'products[1].value': 12.99,
                        'products[1].variant': 'White',
                        'products[2].brand': 'Levis',
                        'products[2].category': 'Merch',
                        'products[2].coupon': 'APPARELSALE',
                        'products[2].currency': 'GBP',
                        'products[2].image_url':
                          'https://www.example.com/product/offer-t-shirt.jpg',
                        'products[2].name': 'T-Shirt-on-offer',
                        'products[2].position': 1,
                        'products[2].price': 12.99,
                        'products[2].product_id': 'offer-t-shirt',
                        'products[2].quantity': 1,
                        'products[2].sku': 'sku-3',
                        'products[2].typeOfProduct': 'Shirt',
                        'products[2].url': 'https://www.example.com/product/offer-t-shirt',
                        'products[2].value': 12.99,
                        'products[2].variant': 'Black',
                        revenue: 31.98,
                        shipping: 4,
                        value: 31.98,
                      },
                      app_version: '1.1.6',
                      current_time: '2020-10-16T13:40:12.791+05:30',
                      user_timezone_offset: 19800,
                      platform: 'web',
                    },
                  ],
                },
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
              },
              files: {},
              userId: 'rudder123',
            },
            statusCode: 200,
          },
        ],
      },
    },
    mockFns,
  },
  {
    name: 'moengage',
    description: 'Test 11: Track event with object data enabled',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              channel: 'web',
              context: {
                timezone: 'Asia/Kolkata',
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              event: 'Order Completed',
              integrations: { All: true },
              messageId: 'a0adfab9-baf7-4e09-a2ce-bbe2844c324a',
              originalTimestamp: '2020-10-16T08:10:12.782Z',
              properties: {
                category: 'some category',
                originalArray: [
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  {
                    nested_field: 'nested value',
                    tags: ['tag_1', 'tag_2', 'tag_3', 'tag_1', 'tag_2', 'tag_3'],
                  },
                  {
                    nested_field: 'nested value',
                    tags: ['tag_1', 'tag_2', 'tag_3', 'tag_1', 'tag_2', 'tag_3'],
                  },
                ],
                checkout_id: 'what is checkout id here??',
                coupon: 'APPARELSALE',
                currency: 'GBP',
                order_id: 'transactionId',
                products: [
                  {
                    brand: '',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/bacon-jam.jpg',
                    name: 'Food/Drink',
                    position: 1,
                    price: 3,
                    product_id: 'product-bacon-jam',
                    quantity: 2,
                    sku: 'sku-1',
                    typeOfProduct: 'Food',
                    url: 'https://www.example.com/product/bacon-jam',
                    value: 6,
                    variant: 'Extra topped',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/t-shirt.jpg',
                    name: 'T-Shirt',
                    position: 2,
                    price: 12.99,
                    product_id: 'product-t-shirt',
                    quantity: 1,
                    sku: 'sku-2',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/t-shirt',
                    value: 12.99,
                    variant: 'White',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    coupon: 'APPARELSALE',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/offer-t-shirt.jpg',
                    name: 'T-Shirt-on-offer',
                    position: 1,
                    price: 12.99,
                    product_id: 'offer-t-shirt',
                    quantity: 1,
                    sku: 'sku-3',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/offer-t-shirt',
                    value: 12.99,
                    variant: 'Black',
                  },
                ],
                revenue: 31.98,
                shipping: 4,
                value: 31.98,
              },
              receivedAt: '2020-10-16T13:40:12.792+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:10:12.783Z',
              timestamp: '2020-10-16T13:40:12.791+05:30',
              type: 'track',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
                useObjectData: true,
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: `https://api-01.moengage.com/v1/event/${secret1}`,
              headers: {
                'Content-Type': 'application/json',
                'MOE-APPKEY': secret1,
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  customer_id: 'rudder123',
                  type: 'event',
                  actions: [
                    {
                      action: 'Order Completed',
                      attributes: {
                        checkout_id: 'what is checkout id here??',
                        coupon: 'APPARELSALE',
                        currency: 'GBP',
                        order_id: 'transactionId',
                        category: 'some category',
                        originalArray: [
                          { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                          { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                          { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                          { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                          { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                          { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                          { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                          {
                            nested_field: 'nested value',
                            tags: ['tag_1', 'tag_2', 'tag_3', 'tag_1', 'tag_2', 'tag_3'],
                          },
                          {
                            nested_field: 'nested value',
                            tags: ['tag_1', 'tag_2', 'tag_3', 'tag_1', 'tag_2', 'tag_3'],
                          },
                        ],
                        products: [
                          {
                            brand: '',
                            category: 'Merch',
                            currency: 'GBP',
                            image_url: 'https://www.example.com/product/bacon-jam.jpg',
                            name: 'Food/Drink',
                            position: 1,
                            price: 3,
                            product_id: 'product-bacon-jam',
                            quantity: 2,
                            sku: 'sku-1',
                            typeOfProduct: 'Food',
                            url: 'https://www.example.com/product/bacon-jam',
                            value: 6,
                            variant: 'Extra topped',
                          },
                          {
                            brand: 'Levis',
                            category: 'Merch',
                            currency: 'GBP',
                            image_url: 'https://www.example.com/product/t-shirt.jpg',
                            name: 'T-Shirt',
                            position: 2,
                            price: 12.99,
                            product_id: 'product-t-shirt',
                            quantity: 1,
                            sku: 'sku-2',
                            typeOfProduct: 'Shirt',
                            url: 'https://www.example.com/product/t-shirt',
                            value: 12.99,
                            variant: 'White',
                          },
                          {
                            brand: 'Levis',
                            category: 'Merch',
                            coupon: 'APPARELSALE',
                            currency: 'GBP',
                            image_url: 'https://www.example.com/product/offer-t-shirt.jpg',
                            name: 'T-Shirt-on-offer',
                            position: 1,
                            price: 12.99,
                            product_id: 'offer-t-shirt',
                            quantity: 1,
                            sku: 'sku-3',
                            typeOfProduct: 'Shirt',
                            url: 'https://www.example.com/product/offer-t-shirt',
                            value: 12.99,
                            variant: 'Black',
                          },
                        ],
                        revenue: 31.98,
                        shipping: 4,
                        value: 31.98,
                      },
                      app_version: '1.1.6',
                      current_time: '2020-10-16T13:40:12.791+05:30',
                      user_timezone_offset: 19800,
                      platform: 'web',
                    },
                  ],
                },
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
              },
              files: {},
              userId: 'rudder123',
            },
            statusCode: 200,
          },
        ],
      },
    },
    mockFns,
  },
  {
    name: 'moengage',
    description: 'Test 12: Track event with invalid timezone',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                timezone: 'Wrong/Timezone',
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              event: 'Order Completed',
              integrations: { All: true },
              messageId: 'a0adfab9-baf7-4e09-a2ce-bbe2844c324a',
              originalTimestamp: '2020-10-16T08:10:12.782Z',
              properties: {
                checkout_id: 'what is checkout id here??',
                coupon: 'APPARELSALE',
                currency: 'GBP',
                order_id: 'transactionId',
                products: [
                  {
                    brand: '',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/bacon-jam.jpg',
                    name: 'Food/Drink',
                    position: 1,
                    price: 3,
                    product_id: 'product-bacon-jam',
                    quantity: 2,
                    sku: 'sku-1',
                    typeOfProduct: 'Food',
                    url: 'https://www.example.com/product/bacon-jam',
                    value: 6,
                    variant: 'Extra topped',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/t-shirt.jpg',
                    name: 'T-Shirt',
                    position: 2,
                    price: 12.99,
                    product_id: 'product-t-shirt',
                    quantity: 1,
                    sku: 'sku-2',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/t-shirt',
                    value: 12.99,
                    variant: 'White',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    coupon: 'APPARELSALE',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/offer-t-shirt.jpg',
                    name: 'T-Shirt-on-offer',
                    position: 1,
                    price: 12.99,
                    product_id: 'offer-t-shirt',
                    quantity: 1,
                    sku: 'sku-3',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/offer-t-shirt',
                    value: 12.99,
                    variant: 'Black',
                  },
                ],
                revenue: 31.98,
                shipping: 4,
                value: 31.98,
              },
              receivedAt: '2020-10-16T13:40:12.792+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:10:12.783Z',
              timestamp: '2020-10-16T13:40:12.791+05:30',
              type: 'track',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: `https://api-01.moengage.com/v1/event/${secret1}`,
              headers: {
                'Content-Type': 'application/json',
                'MOE-APPKEY': secret1,
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  customer_id: 'rudder123',
                  type: 'event',
                  actions: [
                    {
                      action: 'Order Completed',
                      attributes: {
                        checkout_id: 'what is checkout id here??',
                        coupon: 'APPARELSALE',
                        currency: 'GBP',
                        order_id: 'transactionId',
                        'products[0].brand': '',
                        'products[0].category': 'Merch',
                        'products[0].currency': 'GBP',
                        'products[0].image_url': 'https://www.example.com/product/bacon-jam.jpg',
                        'products[0].name': 'Food/Drink',
                        'products[0].position': 1,
                        'products[0].price': 3,
                        'products[0].product_id': 'product-bacon-jam',
                        'products[0].quantity': 2,
                        'products[0].sku': 'sku-1',
                        'products[0].typeOfProduct': 'Food',
                        'products[0].url': 'https://www.example.com/product/bacon-jam',
                        'products[0].value': 6,
                        'products[0].variant': 'Extra topped',
                        'products[1].brand': 'Levis',
                        'products[1].category': 'Merch',
                        'products[1].currency': 'GBP',
                        'products[1].image_url': 'https://www.example.com/product/t-shirt.jpg',
                        'products[1].name': 'T-Shirt',
                        'products[1].position': 2,
                        'products[1].price': 12.99,
                        'products[1].product_id': 'product-t-shirt',
                        'products[1].quantity': 1,
                        'products[1].sku': 'sku-2',
                        'products[1].typeOfProduct': 'Shirt',
                        'products[1].url': 'https://www.example.com/product/t-shirt',
                        'products[1].value': 12.99,
                        'products[1].variant': 'White',
                        'products[2].brand': 'Levis',
                        'products[2].category': 'Merch',
                        'products[2].coupon': 'APPARELSALE',
                        'products[2].currency': 'GBP',
                        'products[2].image_url':
                          'https://www.example.com/product/offer-t-shirt.jpg',
                        'products[2].name': 'T-Shirt-on-offer',
                        'products[2].position': 1,
                        'products[2].price': 12.99,
                        'products[2].product_id': 'offer-t-shirt',
                        'products[2].quantity': 1,
                        'products[2].sku': 'sku-3',
                        'products[2].typeOfProduct': 'Shirt',
                        'products[2].url': 'https://www.example.com/product/offer-t-shirt',
                        'products[2].value': 12.99,
                        'products[2].variant': 'Black',
                        revenue: 31.98,
                        shipping: 4,
                        value: 31.98,
                      },
                      app_version: '1.1.6',
                      current_time: '2020-10-16T13:40:12.791+05:30',
                      platform: 'web',
                    },
                  ],
                },
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
              },
              files: {},
              userId: 'rudder123',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 13: Identify call with iOS device information',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                device: {
                  id: '7e32188a4dab669f',
                  manufacturer: 'Google',
                  model: 'AOSP on IA Emulator',
                  name: 'generic_x86_arm',
                  token: 'desuhere',
                  type: 'ipados',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              integrations: { All: true },
              messageId: '531e3507-1ef5-4a06-b83c-cb521ff34f0c',
              originalTimestamp: '2020-10-16T08:53:29.386Z',
              receivedAt: '2020-10-16T14:23:29.402+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:53:29.387Z',
              timestamp: '2020-10-16T14:23:29.401+05:30',
              type: 'identify',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'customer',
                  attributes: {
                    name: 'Rudder Test',
                    plan: 'Enterprise',
                    email: '<EMAIL>',
                    createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                    'company.id': 'abc123',
                    created_time: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/customer/${secret1}`,
            },
            statusCode: 200,
          },
          {
            output: {
              body: {
                XML: {},
                JSON_ARRAY: {},
                FORM: {},
                JSON: {
                  type: 'device',
                  device_id: '7e32188a4dab669f',
                  attributes: {
                    model: 'AOSP on IA Emulator',
                    push_id: 'desuhere',
                    platform: 'iOS',
                    app_version: '1.1.6',
                  },
                  customer_id: 'rudder123',
                },
              },
              type: 'REST',
              files: {},
              method: 'POST',
              params: {},
              userId: 'rudder123',
              headers: {
                'MOE-APPKEY': secret1,
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/device/${secret1}`,
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 14: Track event with iOS device information and invalid timezone',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                timezone: 'Wrong/Timezone',
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                device: {
                  id: '7e32188a4dab669f',
                  manufacturer: 'Google',
                  model: 'AOSP on IA Emulator',
                  name: 'generic_x86_arm',
                  token: 'desuhere',
                  type: 'ipados',
                },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              event: 'Order Completed',
              integrations: { All: true },
              messageId: 'a0adfab9-baf7-4e09-a2ce-bbe2844c324a',
              originalTimestamp: '2020-10-16T08:10:12.782Z',
              properties: {
                checkout_id: 'what is checkout id here??',
                coupon: 'APPARELSALE',
                currency: 'GBP',
                order_id: 'transactionId',
                products: [
                  {
                    brand: '',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/bacon-jam.jpg',
                    name: 'Food/Drink',
                    position: 1,
                    price: 3,
                    product_id: 'product-bacon-jam',
                    quantity: 2,
                    sku: 'sku-1',
                    typeOfProduct: 'Food',
                    url: 'https://www.example.com/product/bacon-jam',
                    value: 6,
                    variant: 'Extra topped',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/t-shirt.jpg',
                    name: 'T-Shirt',
                    position: 2,
                    price: 12.99,
                    product_id: 'product-t-shirt',
                    quantity: 1,
                    sku: 'sku-2',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/t-shirt',
                    value: 12.99,
                    variant: 'White',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    coupon: 'APPARELSALE',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/offer-t-shirt.jpg',
                    name: 'T-Shirt-on-offer',
                    position: 1,
                    price: 12.99,
                    product_id: 'offer-t-shirt',
                    quantity: 1,
                    sku: 'sku-3',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/offer-t-shirt',
                    value: 12.99,
                    variant: 'Black',
                  },
                ],
                category: 'some category',
                originalArray: [
                  { nested_field: 'nested value', tags: ['tag_1', 'tag_2', 'tag_3'] },
                  { nested_field: 'nested value', tags: ['tag_1'] },
                  { nested_field: 'nested value' },
                ],
                revenue: 31.98,
                shipping: 4,
                value: 31.98,
              },
              receivedAt: '2020-10-16T13:40:12.792+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:10:12.783Z',
              timestamp: '2020-10-16T13:40:12.791+05:30',
              type: 'track',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: `https://api-01.moengage.com/v1/event/${secret1}`,
              headers: {
                'Content-Type': 'application/json',
                'MOE-APPKEY': secret1,
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  customer_id: 'rudder123',
                  device_id: '7e32188a4dab669f',
                  type: 'event',
                  actions: [
                    {
                      action: 'Order Completed',
                      attributes: {
                        checkout_id: 'what is checkout id here??',
                        coupon: 'APPARELSALE',
                        currency: 'GBP',
                        order_id: 'transactionId',
                        'products[0].brand': '',
                        'products[0].category': 'Merch',
                        'products[0].currency': 'GBP',
                        'products[0].image_url': 'https://www.example.com/product/bacon-jam.jpg',
                        'products[0].name': 'Food/Drink',
                        'products[0].position': 1,
                        'products[0].price': 3,
                        'products[0].product_id': 'product-bacon-jam',
                        'products[0].quantity': 2,
                        'products[0].sku': 'sku-1',
                        'products[0].typeOfProduct': 'Food',
                        'products[0].url': 'https://www.example.com/product/bacon-jam',
                        'products[0].value': 6,
                        'products[0].variant': 'Extra topped',
                        'products[1].brand': 'Levis',
                        'products[1].category': 'Merch',
                        'products[1].currency': 'GBP',
                        'products[1].image_url': 'https://www.example.com/product/t-shirt.jpg',
                        'products[1].name': 'T-Shirt',
                        'products[1].position': 2,
                        'products[1].price': 12.99,
                        'products[1].product_id': 'product-t-shirt',
                        'products[1].quantity': 1,
                        'products[1].sku': 'sku-2',
                        'products[1].typeOfProduct': 'Shirt',
                        'products[1].url': 'https://www.example.com/product/t-shirt',
                        'products[1].value': 12.99,
                        'products[1].variant': 'White',
                        'products[2].brand': 'Levis',
                        'products[2].category': 'Merch',
                        'products[2].coupon': 'APPARELSALE',
                        'products[2].currency': 'GBP',
                        'products[2].image_url':
                          'https://www.example.com/product/offer-t-shirt.jpg',
                        'products[2].name': 'T-Shirt-on-offer',
                        'products[2].position': 1,
                        'products[2].price': 12.99,
                        'products[2].product_id': 'offer-t-shirt',
                        'products[2].quantity': 1,
                        'products[2].sku': 'sku-3',
                        'products[2].typeOfProduct': 'Shirt',
                        'products[2].url': 'https://www.example.com/product/offer-t-shirt',
                        'products[2].value': 12.99,
                        'products[2].variant': 'Black',
                        revenue: 31.98,
                        shipping: 4,
                        value: 31.98,
                        'originalArray[0].nested_field': 'nested value',
                        'originalArray[0].tags[0]': 'tag_1',
                        'originalArray[0].tags[1]': 'tag_2',
                        'originalArray[0].tags[2]': 'tag_3',
                        'originalArray[1].nested_field': 'nested value',
                        'originalArray[1].tags[0]': 'tag_1',
                        'originalArray[2].nested_field': 'nested value',
                        category: 'some category',
                      },
                      platform: 'iOS',
                      app_version: '1.1.6',
                      current_time: '2020-10-16T13:40:12.791+05:30',
                    },
                  ],
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: 'rudder123',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 15: Track event with nested object properties and invalid timezone',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              anonymousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              channel: 'web',
              context: {
                timezone: 'Wrong/Timezone',
                app: {
                  build: '1.0.0',
                  name: 'RudderLabs JavaScript SDK',
                  namespace: 'com.rudderlabs.javascript',
                  version: '1.1.6',
                },
                library: { name: 'RudderLabs JavaScript SDK', version: '1.1.6' },
                locale: 'en-GB',
                os: { name: '', version: '' },
                device: {
                  id: '7e32188a4dab669f',
                  manufacturer: 'Google',
                  model: 'AOSP on IA Emulator',
                  name: 'generic_x86_arm',
                  token: 'desuhere',
                  type: 'ipados',
                },
                page: {
                  path: '/testing/script-test.html',
                  referrer: '',
                  search: '',
                  title: '',
                  url: 'http://localhost:3243/testing/script-test.html',
                },
                screen: { density: 2 },
                traits: {
                  company: { id: 'abc123' },
                  createdAt: 'Thu Mar 24 2016 17:46:45 GMT+0000 (UTC)',
                  email: '<EMAIL>',
                  name: 'Rudder Test',
                  plan: 'Enterprise',
                },
                userAgent:
                  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.80 Safari/537.36',
              },
              event: 'Order Completed',
              integrations: { All: true },
              messageId: 'a0adfab9-baf7-4e09-a2ce-bbe2844c324a',
              originalTimestamp: '2020-10-16T08:10:12.782Z',
              properties: {
                checkout_id: 'what is checkout id here??',
                coupon: 'APPARELSALE',
                currency: 'GBP',
                order_id: 'transactionId',
                products: [
                  {
                    brand: '',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/bacon-jam.jpg',
                    name: 'Food/Drink',
                    position: 1,
                    price: 3,
                    product_id: 'product-bacon-jam',
                    quantity: 2,
                    sku: 'sku-1',
                    typeOfProduct: 'Food',
                    url: 'https://www.example.com/product/bacon-jam',
                    value: 6,
                    variant: 'Extra topped',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/t-shirt.jpg',
                    name: 'T-Shirt',
                    position: 2,
                    price: 12.99,
                    product_id: 'product-t-shirt',
                    quantity: 1,
                    sku: 'sku-2',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/t-shirt',
                    value: 12.99,
                    variant: 'White',
                  },
                  {
                    brand: 'Levis',
                    category: 'Merch',
                    coupon: 'APPARELSALE',
                    currency: 'GBP',
                    image_url: 'https://www.example.com/product/offer-t-shirt.jpg',
                    name: 'T-Shirt-on-offer',
                    position: 1,
                    price: 12.99,
                    product_id: 'offer-t-shirt',
                    quantity: 1,
                    sku: 'sku-3',
                    typeOfProduct: 'Shirt',
                    url: 'https://www.example.com/product/offer-t-shirt',
                    value: 12.99,
                    variant: 'Black',
                  },
                ],
                category: 'some category',
                originalArray: {
                  nested_field: 'nested value',
                  tags: ['tag_1', 'tag_2', 'tag_3'],
                  key1: {
                    nested_field: 'nested value',
                    key11: 'val11',
                    key12: 'val12',
                    key13: { k1: 'v1', k2: 'v2' },
                    key2: {},
                    key3: [],
                  },
                },
                revenue: 31.98,
                shipping: 4,
                value: 31.98,
              },
              receivedAt: '2020-10-16T13:40:12.792+05:30',
              request_ip: '[::1]',
              sentAt: '2020-10-16T08:10:12.783Z',
              timestamp: '2020-10-16T13:40:12.791+05:30',
              type: 'track',
              userId: 'rudder123',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              version: '1',
              type: 'REST',
              method: 'POST',
              endpoint: `https://api-01.moengage.com/v1/event/${secret1}`,
              headers: {
                'Content-Type': 'application/json',
                'MOE-APPKEY': secret1,
                Authorization: authHeader1,
              },
              params: {},
              body: {
                JSON: {
                  customer_id: 'rudder123',
                  device_id: '7e32188a4dab669f',
                  type: 'event',
                  actions: [
                    {
                      action: 'Order Completed',
                      attributes: {
                        checkout_id: 'what is checkout id here??',
                        coupon: 'APPARELSALE',
                        currency: 'GBP',
                        order_id: 'transactionId',
                        'products[0].brand': '',
                        'products[0].category': 'Merch',
                        'products[0].currency': 'GBP',
                        'products[0].image_url': 'https://www.example.com/product/bacon-jam.jpg',
                        'products[0].name': 'Food/Drink',
                        'products[0].position': 1,
                        'products[0].price': 3,
                        'products[0].product_id': 'product-bacon-jam',
                        'products[0].quantity': 2,
                        'products[0].sku': 'sku-1',
                        'products[0].typeOfProduct': 'Food',
                        'products[0].url': 'https://www.example.com/product/bacon-jam',
                        'products[0].value': 6,
                        'products[0].variant': 'Extra topped',
                        'products[1].brand': 'Levis',
                        'products[1].category': 'Merch',
                        'products[1].currency': 'GBP',
                        'products[1].image_url': 'https://www.example.com/product/t-shirt.jpg',
                        'products[1].name': 'T-Shirt',
                        'products[1].position': 2,
                        'products[1].price': 12.99,
                        'products[1].product_id': 'product-t-shirt',
                        'products[1].quantity': 1,
                        'products[1].sku': 'sku-2',
                        'products[1].typeOfProduct': 'Shirt',
                        'products[1].url': 'https://www.example.com/product/t-shirt',
                        'products[1].value': 12.99,
                        'products[1].variant': 'White',
                        'products[2].brand': 'Levis',
                        'products[2].category': 'Merch',
                        'products[2].coupon': 'APPARELSALE',
                        'products[2].currency': 'GBP',
                        'products[2].image_url':
                          'https://www.example.com/product/offer-t-shirt.jpg',
                        'products[2].name': 'T-Shirt-on-offer',
                        'products[2].position': 1,
                        'products[2].price': 12.99,
                        'products[2].product_id': 'offer-t-shirt',
                        'products[2].quantity': 1,
                        'products[2].sku': 'sku-3',
                        'products[2].typeOfProduct': 'Shirt',
                        'products[2].url': 'https://www.example.com/product/offer-t-shirt',
                        'products[2].value': 12.99,
                        'products[2].variant': 'Black',
                        revenue: 31.98,
                        shipping: 4,
                        value: 31.98,
                        'originalArray.key1.key11': 'val11',
                        'originalArray.key1.key12': 'val12',
                        'originalArray.key1.key13.k1': 'v1',
                        'originalArray.key1.key13.k2': 'v2',
                        'originalArray.key1.nested_field': 'nested value',
                        'originalArray.nested_field': 'nested value',
                        'originalArray.tags[0]': 'tag_1',
                        'originalArray.tags[1]': 'tag_2',
                        'originalArray.tags[2]': 'tag_3',
                        'originalArray.key1.key2': {},
                        'originalArray.key1.key3': [],
                        category: 'some category',
                      },
                      platform: 'iOS',
                      app_version: '1.1.6',
                      current_time: '2020-10-16T13:40:12.791+05:30',
                    },
                  ],
                },
                JSON_ARRAY: {},
                XML: {},
                FORM: {},
              },
              files: {},
              userId: 'rudder123',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 16: Alias call for user merging',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              messageId: 'adc7c2d0-0ebf-4593-b878-a0eb75932820',
              originalTimestamp: '2023-03-09T00:09:53.235+05:30',
              previousId: '4eb021e9-a2af-4926-ae82-fe996d12f3c5',
              receivedAt: '2023-03-09T00:09:51.292+05:30',
              request_ip: '[::1]',
              rudderId: '1703da0d-2472-459c-9bf0-4e7b66b4673a',
              sentAt: '2023-03-09T00:09:53.235+05:30',
              timestamp: '2023-03-09T00:09:51.291+05:30',
              type: 'alias',
              userId: '12345',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                XML: {},
                FORM: {},
                JSON: {
                  merge_data: [
                    { merged_user: '4eb021e9-a2af-4926-ae82-fe996d12f3c5', retained_user: '12345' },
                  ],
                },
                JSON_ARRAY: {},
              },
              type: 'REST',
              files: {},
              method: 'POST',
              userId: '12345',
              params: {},
              headers: {
                'Content-Type': 'application/json',
                Authorization: authHeader1,
              },
              version: '1',
              endpoint: `https://api-01.moengage.com/v1/customer/merge?app_id=${secret1}`,
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 17: Alias call without previousId validation',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              messageId: 'adc7c2d0-0ebf-4593-b878-a0eb75932820',
              originalTimestamp: '2023-03-09T00:09:53.235+05:30',
              receivedAt: '2023-03-09T00:09:51.292+05:30',
              request_ip: '[::1]',
              rudderId: '1703da0d-2472-459c-9bf0-4e7b66b4673a',
              sentAt: '2023-03-09T00:09:53.235+05:30',
              timestamp: '2023-03-09T00:09:51.291+05:30',
              type: 'alias',
              userId: '12345',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            error: 'Missing required value from "previousId"',
            statTags: {
              destType: 'MOENGAGE',
              errorCategory: 'dataValidation',
              errorType: 'instrumentation',
              feature: 'processor',
              implementation: 'native',
              module: 'destination',
            },
            statusCode: 400,
          },
        ],
      },
    },
  },
  {
    name: 'moengage',
    description: 'Test 18: Identify call without context',
    feature: 'processor',
    module: 'destination',
    version: 'v0',
    input: {
      request: {
        body: [
          {
            message: {
              type: 'identify',
              event: 'identify',
              sentAt: '2023-11-22T03:42:40.346Z',
              userId: 'userId16',
              channel: 'mobile',
              rudderId: 'dummy-rudderId',
              timestamp: '2023-11-22T03:42:31.470Z',
              receivedAt: '2023-11-22T03:42:43.281Z',
              request_ip: '**************',
              anonymousId: 'anon-dummyId-1',
              integrations: {
                All: true,
              },
              originalTimestamp: '2023-11-22T03:42:28.535Z',
            },
            destination: {
              ID: '1iuTZs6eEZVMm6GjRBe6bNShaL3',
              Name: 'MoEngage Testing',
              DestinationDefinition: {
                ID: '1iu4802Tx27kNC4KNYYou6D8jzL',
                Name: 'MOENGAGE',
                DisplayName: 'MoEngage',
                Config: {
                  destConfig: { defaultConfig: ['apiId', 'apiKey', 'region'] },
                  excludeKeys: [],
                  includeKeys: [],
                  supportedSourceTypes: [
                    'android',
                    'ios',
                    'web',
                    'unity',
                    'amp',
                    'cloud',
                    'reactnative',
                  ],
                },
              },
              Config: {
                apiId: secret1,
                apiKey: secret2,
                eventDelivery: false,
                eventDeliveryTS: 1602757086384,
                region: 'US',
              },
              Enabled: true,
              Transformations: [],
              IsProcessorEnabled: true,
            },
          },
        ],
        method: 'POST',
      },
      pathSuffix: '',
    },
    output: {
      response: {
        status: 200,
        body: [
          {
            output: {
              body: {
                FORM: {},
                JSON: {
                  attributes: {},
                  customer_id: 'userId16',
                  type: 'customer',
                },
                JSON_ARRAY: {},
                XML: {},
              },
              endpoint: `https://api-01.moengage.com/v1/customer/${secret1}`,
              files: {},
              headers: {
                Authorization: authHeader1,
                'Content-Type': 'application/json',
                'MOE-APPKEY': secret1,
              },
              method: 'POST',
              params: {},
              type: 'REST',
              userId: 'userId16',
              version: '1',
            },
            statusCode: 200,
          },
        ],
      },
    },
  },
];
