name: Draft New Release

on: workflow_dispatch

jobs:
  draft-new-release:
    name: Draft New Release
    runs-on: ubuntu-latest

    # Only allow release stakeholders to initiate releases
    if: (github.ref == 'refs/heads/develop' || startsWith(github.ref, 'refs/heads/hotfix/')) && (github.actor == 'ItsSudip' || github.actor == 'krishna2020'  || github.actor == 'sandeepdsvs' || github.actor == 'koladilip' || github.actor == 'yashasvibajpai' || github.actor == 'utsabc' || github.actor == 'vinayteki95' || github.actor == 'maheshkutty' || github.actor == 'manish339k') &&  (github.triggering_actor == 'ItsSudip' || github.triggering_actor == 'krishna2020' || github.triggering_actor == 'koladilip' || github.triggering_actor == 'saikumarrs' || github.triggering_actor == 'sandeepdsvs' || github.triggering_actor == 'chandumlg' || github.triggering_actor == 'mihir-4116' || github.triggering_actor == 'yashasvibajpai' || github.triggering_actor == 'utsabc' || github.triggering_actor == 'maheshkutty' || github.triggering_actor == 'manish339k' || github.triggering_actor == 'vinayteki95')
    steps:
      - name: Checkout
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4.4.0
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Install Dependencies
        env:
          HUSKY: 0
        run: |
          npm ci

      # In order to make a commit, we need to initialize a user.
      # You may choose to write something less generic here if you want, it doesn't matter functionality wise.
      - name: Initialize Mandatory Git Config
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      # Calculate the next release version based on conventional semantic release
      - name: Create Release Branch
        id: create-release
        env:
          HUSKY: 0
        run: |
          source_branch_name=${GITHUB_REF##*/}
          release_type=release
          grep -q "hotfix/" <<< "${GITHUB_REF}" && release_type=hotfix-release
          git fetch origin main
          git fetch --tags origin
          git merge origin/main
          current_version=$(jq -r .version package.json)

          npm run release -- --skip.commit --skip.tag --skip.changelog
          new_version=$(jq -r .version package.json)
          git reset --hard

          branch_name="${release_type}/v${new_version}"

          echo "Source branch for new release is $source_branch_name"
          echo "Current version is $current_version"
          echo "Release type is $release_type"
          echo "New version is $new_version"
          echo "New release branch name is $branch_name"
          git checkout -b "$branch_name"
          git push --set-upstream origin "$branch_name"

          echo "source_branch_name=$source_branch_name" >> $GITHUB_OUTPUT
          echo "branch_name=$branch_name" >> $GITHUB_OUTPUT
          echo "new_version=$new_version" >> $GITHUB_OUTPUT
          echo "CURRENT_VERSION_VALUE=$current_version" >> $GITHUB_ENV
          echo "NEW_VERSION_VALUE=$new_version" >> $GITHUB_ENV

      - name: Update Changelog & Bump Version
        id: finish-release
        run: |
          echo "Current version: $CURRENT_VERSION_VALUE"
          echo "New version: $NEW_VERSION_VALUE"
          npx replace $CURRENT_VERSION_VALUE $NEW_VERSION_VALUE sonar-project.properties
          git add sonar-project.properties
          npm run release -- -a --skip.tag --no-verify
          git push

      - name: Create Pull Request
        run: |
          gh pr create \
            --base main \
            --head "${{ steps.create-release.outputs.branch_name }}" \
            --title "chore(release): pull ${{ steps.create-release.outputs.branch_name }} into main" \
            --body ":crown: *An automated PR*"
        env:
          GH_TOKEN: ${{ secrets.PAT }}
