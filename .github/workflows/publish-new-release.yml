name: Publish New GitHub Release

# This workflow creates GitHub releases with Angular conventional commit style release notes
# using a modern GitHub CLI approach instead of the deprecated conventional-github-releaser package

on:
  pull_request:
    types:
      - closed
    branches:
      - main

jobs:
  release:
    name: Publish New GitHub Release
    runs-on: ubuntu-latest

    if: (startsWith(github.event.pull_request.head.ref, 'release/') || startsWith(github.event.pull_request.head.ref, 'hotfix-release/')) && github.event.pull_request.merged == true # only merged pull requests must trigger this job

    steps:
      - name: Extract Version
        id: extract-version
        run: |
          branch_name="${{ github.event.pull_request.head.ref }}"
          version=${branch_name#hotfix-}
          version=${version#release/v}

          echo "release_version=$version" >> $GITHUB_OUTPUT

      - name: Checkout
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4.4.0
        with:
          node-version-file: '.nvmrc'
          cache: 'npm'

      - name: Install Dependencies
        env:
          HUSKY: 0
        run: |
          npm ci

      # In order to make a commit, we need to initialize a user.
      # You may choose to write something less generic here if you want, it doesn't matter functionality wise.
      - name: Initialize Mandatory Git Config
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Tag & Create GitHub Release
        id: create_release
        env:
          HUSKY: 0
          GITHUB_TOKEN: ${{ secrets.PAT }}
          GH_TOKEN: ${{ secrets.PAT }}
        run: |
          echo "🏷️ Checking and creating tag v${{ steps.extract-version.outputs.release_version }}..."
          git fetch --tags origin

          # Check if tag already exists
          if git rev-parse --verify "v${{ steps.extract-version.outputs.release_version }}" >/dev/null 2>&1; then
            echo "✅ Tag v${{ steps.extract-version.outputs.release_version }} already exists, skipping tag creation"
          else
            echo "🏷️ Creating new tag v${{ steps.extract-version.outputs.release_version }}..."
            git tag -a v${{ steps.extract-version.outputs.release_version }} -m "chore: release v${{ steps.extract-version.outputs.release_version }}"
            git push origin refs/tags/v${{ steps.extract-version.outputs.release_version }}
            echo "✅ Tag v${{ steps.extract-version.outputs.release_version }} created and pushed successfully"
          fi

          echo "🚀 Creating GitHub release with conventional commit notes..."
          # Make the script executable
          chmod +x scripts/create-github-release.js

          # Create release using our modern script with Angular conventional commit style notes
          node scripts/create-github-release.js

          echo "✅ Release process completed successfully!"
          echo "DATE=$(date)" >> $GITHUB_ENV

      - name: Verify Release Creation
        id: verify_release
        env:
          GH_TOKEN: ${{ secrets.PAT }}
        run: |
          echo "🔍 Verifying that release v${{ steps.extract-version.outputs.release_version }} exists..."

          # Wait a moment for GitHub API to sync
          sleep 2

          if gh release view v${{ steps.extract-version.outputs.release_version }} > /dev/null 2>&1; then
            echo "✅ Release v${{ steps.extract-version.outputs.release_version }} verified successfully!"
            echo "🔗 Release URL: https://github.com/rudderlabs/rudder-transformer/releases/tag/v${{ steps.extract-version.outputs.release_version }}"

            # Show release info
            echo "📋 Release details:"
            gh release view v${{ steps.extract-version.outputs.release_version }} --json tagName,name,publishedAt,isLatest | jq -r '"Tag: \(.tagName), Published: \(.publishedAt), Latest: \(.isLatest)"'
          else
            echo "❌ Failed to verify release v${{ steps.extract-version.outputs.release_version }}"
            echo "🔍 Checking if tag exists..."
            if git rev-parse --verify "v${{ steps.extract-version.outputs.release_version }}" >/dev/null 2>&1; then
              echo "✅ Tag exists but release not found - this might be a GitHub API delay"
            else
              echo "❌ Tag also missing - release creation completely failed"
            fi
            exit 1
          fi

      - name: Pull Changes Into develop Branch
        run: |
          gh pr create \
            --base develop \
            --head main \
            --title "chore(release): pull main into develop post release v${{ steps.extract-version.outputs.release_version }}" \
            --body ":crown: *An automated PR*"
        env:
          GH_TOKEN: ${{ secrets.PAT }}

      - name: Delete Release Branch
        uses: koj-co/delete-merged-action@master
        if: startsWith(github.event.pull_request.head.ref, 'release/')
        with:
          branches: 'release/*'
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}

      - name: Delete Hotfix Release Branch
        uses: koj-co/delete-merged-action@master
        if: startsWith(github.event.pull_request.head.ref, 'hotfix-release/')
        with:
          branches: 'hotfix-release/*'
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}

      - name: Notify Slack Channel
        id: slack
        uses: slackapi/slack-github-action@v2.1.0
        continue-on-error: true
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
          PROJECT_NAME: 'Rudder Transformer'
          RELEASES_URL: 'https://github.com/rudderlabs/rudder-transformer/releases/tag/'
        with:
          channel-id: ${{ secrets.SLACK_RELEASE_CHANNEL_ID }}
          payload: |
            {
              "text": "*<${{env.RELEASES_URL}}v${{ steps.extract-version.outputs.release_version }}|v${{ steps.extract-version.outputs.release_version }}>*\nCC: <!subteam^S02AEQL26CT> <!subteam^S03SEKBFX0D> <@U021E50QAGY>",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":tada:  ${{ env.PROJECT_NAME }} - New GitHub Release  :tada:"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*<${{env.RELEASES_URL}}v${{ steps.extract-version.outputs.release_version }}|v${{ steps.extract-version.outputs.release_version }}>*\nCC: <!subteam^S02AEQL26CT> <!subteam^S03SEKBFX0D> <@U021E50QAGY>"
                  }
                }
              ]
            }
